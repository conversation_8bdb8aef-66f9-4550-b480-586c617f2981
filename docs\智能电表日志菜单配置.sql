-- 智能电表同步日志菜单配置SQL脚本

-- 1. 插入智能电表同步日志主菜单
INSERT INTO `sys_authorities` (
    `id`, 
    `name`, 
    `code`, 
    `parent_id`, 
    `idx_num`, 
    `auth_url`, 
    `auth_type`, 
    `descn`, 
    `visible`, 
    `perms`, 
    `icon`, 
    `del_flag`, 
    `creator_id`, 
    `creator_name`, 
    `create_time`, 
    `update_by_id`, 
    `update_by_name`, 
    `update_time`, 
    `remark`
) VALUES (
    3849276838270636037, 
    '智能电表同步日志', 
    'SMART_METER_LOG', 
    3849276838270636000, 
    1, 
    '/statistics/smartMeterLog', 
    'M', 
    '智能电表同步日志管理', 
    '0', 
    '', 
    'ios-pulse', 
    '0', 
    3849276838241276000, 
    'admin', 
    NOW(), 
    3849276838241276000, 
    'admin', 
    NOW(), 
    ''
);

-- 2. 插入日志查看子菜单
INSERT INTO `sys_authorities` (
    `id`, 
    `name`, 
    `code`, 
    `parent_id`, 
    `idx_num`, 
    `auth_url`, 
    `auth_type`, 
    `descn`, 
    `visible`, 
    `perms`, 
    `icon`, 
    `del_flag`, 
    `creator_id`, 
    `creator_name`, 
    `create_time`, 
    `update_by_id`, 
    `update_by_name`, 
    `update_time`, 
    `remark`
) VALUES (
    3849276838270636038, 
    '日志查看', 
    'SMART_METER_LOG_VIEW', 
    3849276838270636037, 
    1, 
    '/statistics/smartMeterLog/index', 
    'C', 
    '智能电表同步日志查看', 
    '0', 
    'mssaccount:failSyncLog:list', 
    'ios-list', 
    '0', 
    3849276838241276000, 
    'admin', 
    NOW(), 
    3849276838241276000, 
    'admin', 
    NOW(), 
    ''
);

-- 3. 插入统计分析子菜单
INSERT INTO `sys_authorities` (
    `id`, 
    `name`, 
    `code`, 
    `parent_id`, 
    `idx_num`, 
    `auth_url`, 
    `auth_type`, 
    `descn`, 
    `visible`, 
    `perms`, 
    `icon`, 
    `del_flag`, 
    `creator_id`, 
    `creator_name`, 
    `create_time`, 
    `update_by_id`, 
    `update_by_name`, 
    `update_time`, 
    `remark`
) VALUES (
    3849276838270636039, 
    '统计分析', 
    'SMART_METER_LOG_STATISTICS', 
    3849276838270636037, 
    2, 
    '/statistics/smartMeterLog/statistics', 
    'C', 
    '智能电表同步日志统计分析', 
    '0', 
    'mssaccount:failSyncLog:statistics', 
    'ios-stats', 
    '0', 
    3849276838241276000, 
    'admin', 
    NOW(), 
    3849276838241276000, 
    'admin', 
    NOW(), 
    ''
);

-- 4. 插入相关权限按钮
-- 查看详情权限
INSERT INTO `sys_authorities` (
    `id`, 
    `name`, 
    `code`, 
    `parent_id`, 
    `idx_num`, 
    `auth_url`, 
    `auth_type`, 
    `descn`, 
    `visible`, 
    `perms`, 
    `icon`, 
    `del_flag`, 
    `creator_id`, 
    `creator_name`, 
    `create_time`, 
    `update_by_id`, 
    `update_by_name`, 
    `update_time`, 
    `remark`
) VALUES (
    3849276838270636040, 
    '查看详情', 
    'SMART_METER_LOG_DETAIL', 
    3849276838270636038, 
    1, 
    '', 
    'F', 
    '查看智能电表同步日志详情', 
    '0', 
    'mssaccount:failSyncLog:view', 
    '', 
    '0', 
    3849276838241276000, 
    'admin', 
    NOW(), 
    3849276838241276000, 
    'admin', 
    NOW(), 
    ''
);

-- 更新状态权限
INSERT INTO `sys_authorities` (
    `id`, 
    `name`, 
    `code`, 
    `parent_id`, 
    `idx_num`, 
    `auth_url`, 
    `auth_type`, 
    `descn`, 
    `visible`, 
    `perms`, 
    `icon`, 
    `del_flag`, 
    `creator_id`, 
    `creator_name`, 
    `create_time`, 
    `update_by_id`, 
    `update_by_name`, 
    `update_time`, 
    `remark`
) VALUES (
    3849276838270636041, 
    '更新状态', 
    'SMART_METER_LOG_UPDATE', 
    3849276838270636038, 
    2, 
    '', 
    'F', 
    '更新智能电表同步状态', 
    '0', 
    'mssaccount:failSyncLog:updateStatus', 
    '', 
    '0', 
    3849276838241276000, 
    'admin', 
    NOW(), 
    3849276838241276000, 
    'admin', 
    NOW(), 
    ''
);

-- 说明：
-- auth_type: M=菜单, C=组件, F=按钮
-- visible: 0=显示, 1=隐藏
-- parent_id: 3849276838270636000 是统计分析菜单的ID
-- perms: 权限标识，用于前端权限控制和后端接口权限验证
